name: Electron Build and Release

on:
  workflow_dispatch:
  push:
    branches:
      - electron
    tags:
      - 'v*'

jobs:
  build:
    runs-on: ${{ matrix.os }}
    
    strategy:
      matrix:
        os: [macos-latest, ubuntu-latest, windows-latest]
        node-version: [18.18.0]
      fail-fast: false

    steps:
      - name: Check out Git repository
        uses: actions/checkout@v4

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}

      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 9.14.4
          run_install: false

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v3
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install

      # Install Linux dependencies
      - name: Install Linux dependencies
        if: matrix.os == 'ubuntu-latest'
        run: |
          sudo apt-get update
          sudo apt-get install -y rpm

      # Build
      - name: Build Electron app
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          CSC_LINK: ${{ secrets.CSC_LINK }}
          CSC_KEY_PASSWORD: ${{ secrets.CSC_KEY_PASSWORD }}
          APPLE_ID: ${{ secrets.APPLE_ID }}
          APPLE_APP_SPECIFIC_PASSWORD: ${{ secrets.APPLE_APP_SPECIFIC_PASSWORD }}
          APPLE_TEAM_ID: ${{ secrets.APPLE_TEAM_ID }}
          NODE_OPTIONS: "--max_old_space_size=4096"
        run: |
          if [ "$RUNNER_OS" == "Windows" ]; then
            pnpm run electron:build:win
          elif [ "$RUNNER_OS" == "macOS" ]; then
            pnpm run electron:build:mac
          else
            pnpm run electron:build:linux
          fi
        shell: bash

      # Create Release
      - name: Create Release
        uses: softprops/action-gh-release@v2
        with:
          draft: ${{ github.ref_type == 'branch' }}
          files: |
            dist/*.exe
            dist/*.dmg
            dist/*.deb
            dist/*.AppImage
            dist/*.zip
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
