---
name: Epic
about: Epics define long-term vision and capabilities of the software. They will never be finished but serve as umbrella for features.
title: ''
labels:
  - epic
assignees: ''
---

# Strategic Impact

<!-- Why does this area matter? How is it integrated into the product or the development process? What would happen if we ignore it? -->

# Target Audience

<!-- Who benefits most from improvements in this area?

Usual values: Software Developers using the IDE | Contributors -->

# Capabilities

<!-- which existing capabilities or future features can be imagined that belong to this epic? This list serves as illustration to sketch the boundaries of this epic.
Once features are actually being planned / described in detail, they can be linked here. -->
